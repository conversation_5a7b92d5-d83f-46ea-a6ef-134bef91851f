package com.digiwin.escloud.aiomail.amqp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dysmsapi20170525.models.SendSmsResponseBody;
import com.digiwin.escloud.aioitms.model.device.DeviceWarningNotifyMapping;
import com.digiwin.escloud.aiomail.amqp.dao.EDRV2Mapper;
import com.digiwin.escloud.aiomail.amqp.dao.TmpRhMapper;
import com.digiwin.escloud.aiomail.common.Constants;
import com.digiwin.escloud.aiomail.common.RedisNameHelp;
import com.digiwin.escloud.aiomail.freemarker.FreemarkerService;
import com.digiwin.escloud.aiomail.model.FreemarkerTplData;
import com.digiwin.escloud.aiomail.model.MailLog;
import com.digiwin.escloud.aiomail.model.MailSourceType;
import com.digiwin.escloud.aiomail.model.WarningNotice;
import com.digiwin.escloud.aiomail.properties.AioMailProperties;
import com.digiwin.escloud.aiomail.services.Impl.MailService;
import com.digiwin.escloud.aiomail.services.Impl.SmsService;
import com.digiwin.escloud.aiomail.services.Impl.WechatService;
import com.digiwin.escloud.aiomail.util.CommonUtils;
import com.digiwin.escloud.aiomail.util.MessageUtils;
import com.digiwin.escloud.aiouser.model.notify.NotifyTargetType;
import com.digiwin.escloud.aiouser.model.notify.UserNotifySetting;
import com.digiwin.escloud.aiouser.model.tenantNotice.TenantNotifyGroupRespDTO;
import com.digiwin.escloud.aiouser.model.tenantNotice.UserNotifyContact;
import com.digiwin.escloud.common.feign.AioItmsFeignClient;
import com.digiwin.escloud.common.feign.AioUserFeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.*;
import com.digiwin.escloud.integration.api.emc.req.EmcEventMsg;
import com.digiwin.escloud.integration.common.IamBaseResponse;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ResourceUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.mail.internet.MimeMessage;
import java.io.IOException;
import java.net.URI;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.digiwin.escloud.aiomail.common.Constants.DigiwinAppNotify.TEMPORARY_NO_NOTIFY_HOURS;

/**
 * @Date 2021/5/27 9:30
 * @Created yanggld
 * @Description
 */
@Slf4j
@Component
@RabbitListener(queues = {MqConstant.WARNING_NOTICE_QUEUE}, concurrency = "10")
public class RabbitNoticeQueueListenerHandler implements EnvironmentAware, RedisNameHelp {

    @Value("${service.area}")
    private String serviceArea;
    @Value("${digiwin.aiops.mis.login.url}")
    private String misLoginUrl;
    @Autowired
    private FreemarkerService tplService;
    @Autowired
    private JavaMailSender mailSender;
    @Autowired
    @Qualifier(value = "primaryMongoTemplate")
    MongoTemplate mongoTemplate;
    private int maxTryNum = 3;
    @Autowired
    private AioUserFeignClient aioUserFeignClient;
    @Autowired
    private AioItmsFeignClient aioItmsFeignClient;
    @Autowired
    private WechatService wechatService;
    @Autowired
    private SmsService smsService;
    @Autowired
    private AioMailProperties aioMailProperties;
    @Autowired
    private MessageUtils messageUtils;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private TmpRhMapper tmpRhMapper;
    @Autowired
    private EDRV2Mapper edrv2Mapper;
    @Autowired
    private CommonUtils commonUtils;
    @Autowired
    private MailService mailService;

    private String getMailTitle(Map<String, Object> body) {
        String aiopsItem = Objects.toString(body.get("aiopsItem"), "");
        String aiId = Objects.toString(body.get("aiId"), "0");
        if ("TMP_RH".equals(aiopsItem)) {
            return tmpRhMapper.getDevicePlacementPoint(Long.parseLong(aiId));
        }
        if ("S1EDR_HOST".equalsIgnoreCase(aiopsItem)) {
            return edrv2Mapper.getInstanceName(Long.parseLong(aiId));
        }
        return null;
    }

    /**
     * 1. 根据body中的参数eid，deviceId,预警项编号。获取到to,cc 发邮件。
     * 2. 记录发送的日志。
     *
     * @param msg
     * @param body
     * @param channel
     */
    @RabbitHandler
    public void receiveWarningNotice(Message msg, Map<String, Object> body, Channel channel) {
        if (log.isDebugEnabled()) {
            log.debug("接收到预警数据:{}", JSONObject.toJSONString(body));
        }
        try {
            if (aioMailProperties.isNotice()) {
                String aiopsItem = Objects.toString(body.get("aiopsItem"), "");
                String mailTitle = this.getMailTitle(body);
                if (StringUtil.isNotEmpty(mailTitle) && "TMP_RH".equalsIgnoreCase(aiopsItem)) {
                    body.put("mailTitle", mailTitle);
                }
                if (StringUtil.isNotEmpty(mailTitle) && "S1EDR_HOST".equalsIgnoreCase(aiopsItem)) {
                    body.put("mailTitle", mailTitle);
                    body.put("aiopsInstanceName", mailTitle);
                }
                //客服消息
                WarningNotice customerServiceUserWarningNotice = new WarningNotice();
                buildCustomerServiceUserWarningNotice(body, customerServiceUserWarningNotice);
                notice(body, customerServiceUserWarningNotice);
                //客户消息
                WarningNotice tenantUserWarningNotice = new WarningNotice();
                buildTenantUserWarningNotice(body, tenantUserWarningNotice);
                notice(body, tenantUserWarningNotice);
                //客服消息
                WarningNotice attentionUserWarningNotice = new WarningNotice();
                buildAttentionUserWarningNotice(body, attentionUserWarningNotice);
                notice(body, attentionUserWarningNotice);
                //客户消息
                WarningNotice digiwinAppWarningNotice = new WarningNotice();
                buildDigiwinAppWarningNotice(body, digiwinAppWarningNotice);
                notice(body, digiwinAppWarningNotice);
            }
            MessageProperties messageProperties = msg.getMessageProperties();
            long deliveryTag = messageProperties.getDeliveryTag();
            try {
                channel.basicAck(deliveryTag, false);
            } catch (IOException e) {
                e.printStackTrace();
            }
        } catch (Exception e) {
            MessageProperties messageProperties = msg.getMessageProperties();
            long deliveryTag = messageProperties.getDeliveryTag();
            try {
                channel.basicNack(deliveryTag, false, false);
            } catch (IOException ex) {
                ex.printStackTrace();
            }
            e.printStackTrace();
            log.error("预警通知异常~~~~~~~~~~~~");
        }
    }

    private void buildDigiwinAppWarningNotice(Map<String, Object> body, WarningNotice warningNotice) throws Exception {
        String eidString = getValStr(body, "eid");
        //由放入queue的地方控管，这里不多做处理
//        String warningLevel = getValStr(body, "warningLevel");
//        if (!WarningLevel.FATAL.isSame(warningLevel)) {
//            log.info("eid:{}, 预警级别:{}, 不发送APP预警通知", eidString, warningLevel);
//            return;
//        }
        BaseResponse<List<UserNotifySetting>> response = aioUserFeignClient.getUserNotifySettingByTenant(0L,
                LongUtil.objectToLong(eidString), NotifyTargetType.DINGDING.name(), "WARNING",
                true, "AIEOM");
        if (!response.checkIsSuccess()) {
            log.error("eid:{}, 取得用户设定异常:{}", eidString, response.getErrMsg());
        }
        if (log.isDebugEnabled()) {
            log.debug("数据:{}, 取得用户设定：{}", JSONObject.toJSONString(body), JSONObject.toJSONString(response));
        }
        List<UserNotifySetting> unsList = response.getData();
        if (CollectionUtils.isEmpty(unsList)) {
            log.info("eid:{}, 取得用户设定为空, 不发送APP预警通知", eidString);
            return;
        }

        // APP数据
        WarningNotice.WarningDigiwinApp warningDigiwinApp = new WarningNotice.WarningDigiwinApp();
        buildWarningDigiwinAppParams(warningDigiwinApp, eidString, body);
        warningDigiwinApp.setUserIdList(unsList.stream().map(x -> x.getUserId()).collect(Collectors.toList()));
        warningNotice.setWarningDigiwinApp(warningDigiwinApp);
    }

    private void buildAttentionUserWarningNotice(Map<String, Object> body, WarningNotice warningNotice) throws Exception {
        String eid = getValStr(body, "eid");
        ResponseBase responseBase = aioUserFeignClient.getAttentionUserByEid(eid, true);
        log.debug("数据:{},得到的关注用户邮箱：{}", JSONObject.toJSONString(body), JSONObject.toJSONString(responseBase));
        Object data = responseBase.getData();
        if (data != null) {
            List<Map<String, Object>> userList = (List<Map<String, Object>>) data;
            List<String> toList = new ArrayList<>();
            List<String> telephoneList = new ArrayList<>();
            List<String> wechatList = new ArrayList<>();
            for (Map<String, Object> map : userList) {
                String email = getValStr(map, "email");
                if (!StringUtils.isEmpty(email)) {
                    toList.add(email);
                }
                String telephone = getValStr(map, "telephone");
                if (!StringUtils.isEmpty(telephone)) {
                    telephoneList.add(telephone);
                }
                String wechat = getValStr(map, "wechat");
                if (!StringUtils.isEmpty(wechat)) {
                    wechatList.add(wechat);
                }
            }
            FreemarkerTplData tplData = buildFreemarkerTplData(body);
            String templateContent = tplService.getByTemplateName("warning2.ftl", tplData);
            String title = Objects.toString(tplData.getParams().get("title"), "");
            String tenantName = Objects.toString(body.get("tenantName"), "");
            String deviceName = Objects.toString(body.get("deviceName"), "");
            String warningLevelCode = Objects.toString(body.get("warningLevel"), "");
            String warningLevelMessage = WarningLevel.getByCode(warningLevelCode).getMessage();
            String warningLevel = messageUtils.get(warningLevelMessage, tplData.getI18n());
            String warningS1EDR = messageUtils.get("warning_s1edr", tplData.getI18n());
            String ipAddress = Objects.toString(body.get("ipAddress"), "");
            String aiopsItemName = Objects.toString(body.get("aiopsItemName"), "");
            String aiopsInstanceName = Objects.toString(body.get("aiopsInstanceName"), "");
            String newTitle = "[" + warningLevel + "][" + tenantName + "] [" + aiopsItemName + "] [" + aiopsInstanceName + "] " + title;
//            body.put("newTitle", this.getMailTitle(body, newTitle));
            String aiopsItem = Objects.toString(body.get("aiopsItem"), "");
            String mailTitle = this.getMailTitle(body);
            if (StringUtil.isNotEmpty(mailTitle) && "TMP_RH".equalsIgnoreCase(aiopsItem)) {
                newTitle = "[" + warningLevel + "][" + tenantName + "] [" + aiopsItemName + "] [" + aiopsInstanceName + "] [" + mailTitle + "] " + title;
            }
            if (StringUtil.isNotEmpty(mailTitle) && "S1EDR_HOST".equalsIgnoreCase(aiopsItem)) {
                newTitle = "[" + warningLevel + "][" + tenantName + "] [" + warningS1EDR + "] [" + mailTitle + "] " + title;
            }
            String warningTime = Objects.toString(body.get("warningTime"), "");
            String warningCount = Objects.toString(body.get("warningCount"), "");
            String suggest = Objects.toString(body.get("suggest"), "");
            String warningName = Objects.toString(body.get("warningName"), "");
            String warningRowKey = Objects.toString(body.get("warningRowKey"), "");

            if (CollectionUtils.isEmpty(toList)) {
                log.debug("未找到关注用户收件人：{}", JSONObject.toJSONString(body));
            } else {
                // 邮件数据
                WarningNotice.WarningMail warningMail = new WarningNotice.WarningMail();
                warningMail.setFrom(env.getProperty("spring.mail.username"));
                warningMail.setTo(toList.toArray(new String[toList.size()]));
                warningMail.setI18n(tplData.getI18n());
                warningMail.setSubject(newTitle);
                warningMail.setText(templateContent);
                warningNotice.setWarningMail(warningMail);
            }

            // 微信数据
            if (CollectionUtils.isEmpty(wechatList)) {
                log.debug("未找到关注微信：{}", JSONObject.toJSONString(body));
            } else {
                WarningNotice.WarningWechat warningWechat = new WarningNotice.WarningWechat();
                List<String> tousers = new ArrayList<>();
                String url = buildWeChatUrl(aioMailProperties.getWechat().getDetailUrl(), warningRowKey, null);
                buildWarningWechatParams(warningWechat, warningName, warningTime, warningCount, suggest, url);
                for (String wechat : wechatList) {
                    if (!StringUtils.isEmpty(wechat)) {
                        tousers.add(wechat);
                    }
                }
                warningWechat.setTousers(tousers);
                warningNotice.setWarningWechat(warningWechat);
            }
            // 短信数据
            if (CollectionUtils.isEmpty(telephoneList)) {
                log.debug("未找到关注短信：{}", JSONObject.toJSONString(body));
            } else {
                WarningNotice.WarningSms warningSms = new WarningNotice.WarningSms();
                List<String> phoneNumbers = new ArrayList<>();
                buildWarningSmsParams(warningSms, tenantName, deviceName, title, "#/login");
                warningSms.setEventId(aioMailProperties.getSms().getEmcWarningEventId());
                for (String telephone : telephoneList) {
                    if (!StringUtils.isEmpty(telephone)) {
                        phoneNumbers.add(telephone);
                    }
                    warningSms.setPhoneNumbers(phoneNumbers);
                }
                warningNotice.setWarningSms(warningSms);
            }
        }
    }

    private void buildTenantUserWarningNotice(Map<String, Object> body, WarningNotice warningNotice) throws Exception {
        String adcwId = Objects.toString(body.get("adcwId"), "");

        if (StringUtils.isEmpty(adcwId)) {
            return;
        }
        String eid = getValStr(body, "eid");
        List<DeviceWarningNotifyMapping> deviceWarningNotifyMappingList =
                aioItmsFeignClient.selectDeviceWarningNotifyMapping(Long.parseLong(adcwId), "");

        log.info("deviceWarningNotifyMappingList 數據: {}", deviceWarningNotifyMappingList);

        List<UserNotifyContact> userNotifyContactList = new ArrayList<>();
        if (CollectionUtils.isEmpty(deviceWarningNotifyMappingList)) {
            //直接取默认的通知群组
            BaseResponse<List<TenantNotifyGroupRespDTO>> response =
                    aioUserFeignClient.getUserNoticeContactByServiceCode(eid, true);
            log.info("数据:{},得到的客户默认通知：{}", JSONObject.toJSONString(body), JSONObject.toJSONString(response));
            List<TenantNotifyGroupRespDTO> tngrList;
            if (response.checkIsSuccess() && !CollectionUtils.isEmpty(tngrList = response.getData())) {
                List<UserNotifyContact> uncList = tngrList.stream()
                        .map(TenantNotifyGroupRespDTO::getUserNotifyContactList)
                        .filter(x -> !CollectionUtils.isEmpty(x))
                        .flatMap(Collection::stream).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(uncList)) {
                    userNotifyContactList.addAll(uncList);
                }
            }
        } else {
            log.info("数据:{},得到的客户通知：{}", JSONObject.toJSONString(body),
                    JSONObject.toJSONString(deviceWarningNotifyMappingList));
            List<Long> tenantNotifyGroupIdList = new ArrayList<>();
            List<String> mailList = new ArrayList<>();
            for (DeviceWarningNotifyMapping deviceWarningNotifyMapping : deviceWarningNotifyMappingList) {
                String notifyCategory = deviceWarningNotifyMapping.getNotifyCategory();
                String sourceId = deviceWarningNotifyMapping.getSourceId();
                if ("BY_USER".equals(notifyCategory)) {
                    if (!StringUtils.isEmpty(sourceId)) {
                        mailList.add(sourceId);
                    }
                } else if ("BY_GROUP".equals(notifyCategory)) {
                    if (!StringUtils.isEmpty(sourceId)) {
                        tenantNotifyGroupIdList.add(Long.parseLong(sourceId));
                    }
                }
            }

            log.info("mailList 數據:{}", mailList);

            if (!CollectionUtils.isEmpty(tenantNotifyGroupIdList)) {
                ResponseBase userNoticeContactResp1 = aioUserFeignClient.getUserNoticeContact(tenantNotifyGroupIdList);
                log.info("userNoticeContactResp1 數據:{}", userNoticeContactResp1);
                if (userNoticeContactResp1.getData() != null) {
                    List<UserNotifyContact> userNotifyContactList1 =
                            JSONObject.parseArray(JSONObject.toJSONString(userNoticeContactResp1.getData()),
                                    UserNotifyContact.class);
                    userNotifyContactList.addAll(userNotifyContactList1);
                }
            }
            if (!CollectionUtils.isEmpty(mailList)) {
                ResponseBase userNoticeContactResp2 = aioUserFeignClient.getUserNoticeContactByMail(eid, mailList);
                log.info("userNoticeContactResp2 數據:{}", userNoticeContactResp2);
                if (userNoticeContactResp2.getData() != null) {
                    List<UserNotifyContact> userNotifyContactList2 =
                            JSONObject.parseArray(JSONObject.toJSONString(userNoticeContactResp2.getData()),
                                    UserNotifyContact.class);
                    userNotifyContactList.addAll(userNotifyContactList2);
                }
            }
        }

        log.info("userNotifyContactList 數據:{}", userNotifyContactList);

        FreemarkerTplData tplData = buildFreemarkerTplData(body);
        String templateContent = tplService.getByTemplateName("warning2.ftl", tplData);
        String title = Objects.toString(tplData.getParams().get("title"), "");
        String tenantName = Objects.toString(body.get("tenantName"), "");
        String deviceName = Objects.toString(body.get("deviceName"), "");
        String ipAddress = Objects.toString(body.get("ipAddress"), "");
        String aiopsItemName = Objects.toString(body.get("aiopsItemName"), "");
        String aiopsInstanceName = Objects.toString(body.get("aiopsInstanceName"), "");
        String warningLevelCode = Objects.toString(body.get("warningLevel"), "");
        String warningLevelMessage = WarningLevel.getByCode(warningLevelCode).getMessage();
        String warningLevel = messageUtils.get(warningLevelMessage, tplData.getI18n());
        String warningS1EDR = messageUtils.get("warning_s1edr", tplData.getI18n());
        String newTitle = "[" + warningLevel + "][" + tenantName + "] [" + aiopsItemName + "] [" + aiopsInstanceName + "] " + title;
//        String newTitle = "客户[" + tenantName + "]设备[" + deviceName + "]([" + ipAddress + "])" + title;
        String aiopsItem = Objects.toString(body.get("aiopsItem"), "");
        String mailTitle = this.getMailTitle(body);
        if (StringUtil.isNotEmpty(mailTitle) && "TMP_RH".equalsIgnoreCase(aiopsItem)) {
            newTitle = "[" + warningLevel + "][" + tenantName + "] [" + aiopsItemName + "] [" + aiopsInstanceName + "] [" + mailTitle + "] " + title;
        }
        if (StringUtil.isNotEmpty(mailTitle) && "S1EDR_HOST".equalsIgnoreCase(aiopsItem)) {
            newTitle = "[" + warningLevel + "][" + tenantName + "] [" + warningS1EDR + "] [" + mailTitle + "] " + title;
        }
        String warningTime = Objects.toString(body.get("warningTime"), "");
        String warningCount = Objects.toString(body.get("warningCount"), "");
        String suggest = Objects.toString(body.get("suggest"), "");
        String warningName = Objects.toString(body.get("warningName"), "");
        String warningRowKey = Objects.toString(body.get("warningRowKey"), "");

        Set<String> mailSet = new HashSet<>();
        Set<String> telephoneSet = new HashSet<>();
        Set<String> wechatSet = new HashSet<>();
        HashMap<String, String> wechatMap = new HashMap<>();
        for (UserNotifyContact userNotifyContact : userNotifyContactList) {
            //当用户设置关闭预警将不会接收任何通知
            if (!userNotifyContact.isEnabled()) {
                continue;
            }
            //需要过滤掉未设置预警级别的通知方式
            String receiveLevels = Optional.ofNullable(userNotifyContact.getReceiveLevels()).orElse("");
            if (!receiveLevels.contains(warningLevelCode)) {
                continue;
            }
            //需要过滤预警通知方式
            String notifyWays = Optional.ofNullable(userNotifyContact.getNotifyWays()).orElse("");
            if (!StringUtils.isEmpty(userNotifyContact.getEmail()) && notifyWays.contains(Constants.NotifyWay.MAIL)) {
                mailSet.add(userNotifyContact.getEmail());
            }
            if (!StringUtils.isEmpty(userNotifyContact.getTelephone()) && notifyWays.contains(Constants.NotifyWay.SMS)) {
                telephoneSet.add(userNotifyContact.getTelephone());
            }
            if (!StringUtils.isEmpty(userNotifyContact.getWechat()) && notifyWays.contains(Constants.NotifyWay.WECHAT)) {
                wechatSet.add(userNotifyContact.getWechat());
                if (StringUtil.isNotEmpty(userNotifyContact.getUserId())) {
                    wechatMap.put(userNotifyContact.getWechat(), userNotifyContact.getUserId());
                }
            }
        }

//        body.put("newTitle", tmpRhMailTitle);
        if (CollectionUtils.isEmpty(mailSet)) {
            log.error("未找到客户邮箱：{}", JSONObject.toJSONString(body));
        } else {
            log.info("数据:{},得到的客户邮箱：{}", JSONObject.toJSONString(body), JSONObject.toJSONString(mailSet));
            // 邮件数据
            WarningNotice.WarningMail warningMail = new WarningNotice.WarningMail();
            warningMail.setFrom(env.getProperty("spring.mail.username"));
            warningMail.setTo(mailSet.toArray(new String[0]));
            warningMail.setI18n(tplData.getI18n());
            warningMail.setSubject(newTitle);
            warningMail.setText(templateContent);
            warningNotice.setWarningMail(warningMail);
        }
        // 短信数据
        if (CollectionUtils.isEmpty(telephoneSet)) {
            log.error("未找到客户电话：{}", JSONObject.toJSONString(body));
        } else {
            log.info("数据:{},得到的客户电话：{}", JSONObject.toJSONString(body), JSONObject.toJSONString(telephoneSet));
            WarningNotice.WarningSms warningSms = new WarningNotice.WarningSms();
            List<String> phoneNumbers = new ArrayList<>();
            buildWarningSmsParams(warningSms, tenantName, deviceName, title, "#/login");
            warningSms.setEventId(aioMailProperties.getSms().getEmcMisWarningEventId());
            for (String telephone : telephoneSet) {
                if (!StringUtils.isEmpty(telephone)) {
                    phoneNumbers.add(telephone);
                }
                warningSms.setPhoneNumbers(phoneNumbers);
            }
            warningNotice.setWarningSms(warningSms);
        }
        //微信
        if (CollectionUtils.isEmpty(wechatSet)) {
            log.info("未找到客户微信：{}", JSONObject.toJSONString(body));
        } else {
            WarningNotice.WarningWechat warningWechat = new WarningNotice.WarningWechat();
            List<String> tousers = new ArrayList<>();
            HashMap<String, String> userUrls = new HashMap<>();
            String url = buildWeChatUrl(aioMailProperties.getWechat().getDetailUrl(), warningRowKey, null);
            buildWarningWechatParams(warningWechat, warningName, warningTime, warningCount, suggest, url);
            for (String wechat : wechatSet) {
                if (!StringUtils.isEmpty(wechat)) {
                    tousers.add(wechat);
                    String userId = wechatMap.get(wechat);
                    String userUrl = buildWeChatUrl(aioMailProperties.getWechat().getDetailUrl(), warningRowKey, userId);
                    userUrls.put(wechat, userUrl);
                }
            }
            warningWechat.setTousers(tousers);
            warningWechat.setUserUrls(userUrls);
            warningNotice.setWarningWechat(warningWechat);
        }
    }

    private void buildCustomerServiceUserWarningNotice(Map<String, Object> body, WarningNotice warningNotice) throws Exception {
        String collectCode = getValStr(body, "accId");
        String warningCode = getValStr(body, "warningCode");
        String warningLevelCode = getValStr(body, "warningLevel");
        String warningDevice = getValStr(body, "deviceId");
        String warningDeviceType = getValStr(body, "warningDeviceType");
        String serviceCode = getValStr(body, "eid");
        ResponseBase responseBase = aioUserFeignClient.getNoticeGroupMail(collectCode, warningCode, warningLevelCode, warningDevice, warningDeviceType, serviceCode);
        log.info("数据:{},得到的客服邮箱：{}", JSONObject.toJSONString(body), JSONObject.toJSONString(responseBase));
        Object data = responseBase.getData();
        if (data != null) {
            List<JSONObject> jsonObjects = JSONObject.parseArray(JSONObject.toJSONString(data), JSONObject.class);
            List<String> ccList = new ArrayList<>();
            List<String> toList = new ArrayList<>();
            List<String> telephoneList = new ArrayList<>();
            List<String> wechatList = new ArrayList<>();
            for (JSONObject dataJson : jsonObjects) {
                String mailCC = dataJson.getString("mailCC");
                if (!StringUtils.isEmpty(mailCC)) {
                    String[] cc = mailCC.split(";");
                    ccList.addAll(CollectionUtils.arrayToList(cc));
                }
                JSONArray staffUserInfos = dataJson.getJSONArray("groupStaffUserInfos");
                if (!staffUserInfos.isEmpty()) {
                    for (int i = 0; i < staffUserInfos.size(); i++) {
                        JSONObject userJson = staffUserInfos.getJSONObject(i);
                        String email = userJson.getString("email");
                        toList.add(email);
                        String telephone = userJson.getString("telephone");
                        telephoneList.add(telephone);
                        String wechat = userJson.getString("wechat");
                        wechatList.add(wechat);
                    }
                }
            }
            FreemarkerTplData tplData = buildFreemarkerTplData(body);
            String templateContent = tplService.getByTemplateName("warning2.ftl", tplData);
            String title = Objects.toString(tplData.getParams().get("title"), "");
            String tenantName = Objects.toString(body.get("tenantName"), "");
            String deviceName = Objects.toString(body.get("deviceName"), "");
            String ipAddress = Objects.toString(body.get("ipAddress"), "");
            String aiopsItemName = Objects.toString(body.get("aiopsItemName"), "");
            String aiopsInstanceName = Objects.toString(body.get("aiopsInstanceName"), "");
            String warningLevelMessage = WarningLevel.getByCode(warningLevelCode).getMessage();
            String warningLevel = messageUtils.get(warningLevelMessage, tplData.getI18n());
            String warningS1EDR = messageUtils.get("warning_s1edr", tplData.getI18n());
            String newTitle = "[" + warningLevel + "][" + tenantName + "] [" + aiopsItemName + "] [" + aiopsInstanceName + "] " + title;
//            String newTitle = "客户[" + tenantName + "]设备[" + deviceName + "]([" + ipAddress + "])" + title;
//            body.put("newTitle", this.getMailTitle(body, newTitle));
            String aiopsItem = Objects.toString(body.get("aiopsItem"), "");
            String mailTitle = Objects.toString(body.get("mailTitle"), "");
            if (StringUtil.isNotEmpty(mailTitle) && "TMP_RH".equalsIgnoreCase(aiopsItem)) {
                newTitle = "[" + warningLevel + "][" + tenantName + "] [" + aiopsItemName + "] [" + aiopsInstanceName + "] [" + mailTitle + "] " + title;
            }
            if (!StringUtil.isNotEmpty(mailTitle) && "S1EDR_HOST".equalsIgnoreCase(aiopsItem)) {
                newTitle = "[" + warningLevel + "][" + tenantName + "] [" + warningS1EDR + "] [" + mailTitle + "] " + title;
            }
            String warningTime = Objects.toString(body.get("warningTime"), "");
            String warningCount = Objects.toString(body.get("warningCount"), "");
            String suggest = Objects.toString(body.get("suggest"), "");
            String warningName = Objects.toString(body.get("warningName"), "");
            String warningRowKey = Objects.toString(body.get("warningRowKey"), "");

            if (CollectionUtils.isEmpty(toList)) {
                log.info("未找到客服收件人：{}", JSONObject.toJSONString(body));
            } else {
                // 邮件数据
                WarningNotice.WarningMail warningMail = new WarningNotice.WarningMail();
                warningMail.setFrom(env.getProperty("spring.mail.username"));
                if (!CollectionUtils.isEmpty(ccList)) {
                    warningMail.setCc(ccList.toArray(new String[ccList.size()]));
                }
                warningMail.setTo(toList.toArray(new String[toList.size()]));
                warningMail.setI18n(tplData.getI18n());
                warningMail.setSubject(newTitle);
                warningMail.setText(templateContent);
                warningNotice.setWarningMail(warningMail);
            }

            // 微信数据
            if (CollectionUtils.isEmpty(wechatList)) {
                log.info("未找到客服微信：{}", JSONObject.toJSONString(body));
            } else {
                WarningNotice.WarningWechat warningWechat = new WarningNotice.WarningWechat();
                List<String> tousers = new ArrayList<>();
                String url = buildWeChatUrl(aioMailProperties.getWechat().getDetailUrl(), warningRowKey, null);
                buildWarningWechatParams(warningWechat, warningName, warningTime, warningCount, suggest, url);
                for (String wechat : wechatList) {
                    if (!StringUtils.isEmpty(wechat)) {
                        tousers.add(wechat);
                    }
                }
                warningWechat.setTousers(tousers);
                warningNotice.setWarningWechat(warningWechat);
            }
            // 短信数据
            if (CollectionUtils.isEmpty(telephoneList)) {
                log.info("未找到客服短信：{}", JSONObject.toJSONString(body));
            } else {
                WarningNotice.WarningSms warningSms = new WarningNotice.WarningSms();
                List<String> phoneNumbers = new ArrayList<>();
                buildWarningSmsParams(warningSms, tenantName, deviceName, title, "#/login");
                warningSms.setEventId(aioMailProperties.getSms().getEmcWarningEventId());
                for (String telephone : telephoneList) {
                    if (!StringUtils.isEmpty(telephone)) {
                        phoneNumbers.add(telephone);
                    }
                    warningSms.setPhoneNumbers(phoneNumbers);
                }
                warningNotice.setWarningSms(warningSms);
            }
        }
    }

    private void buildWarningDigiwinAppParams(WarningNotice.WarningDigiwinApp warningDigiwinApp, String eidString,
                                              Map<String, Object> body) {
        FreemarkerTplData tplData = buildFreemarkerTplData(body);
        String title = Objects.toString(tplData.getParams().get("title"), "");
        //String content = Objects.toString(tplData.getParams().get("content"), "");
        //String tenantName = Objects.toString(body.get("tenantName"), "");
        //String deviceName = Objects.toString(body.get("deviceName"), "");
        //String ipAddress = Objects.toString(body.get("ipAddress"), "");
        String aiopsItemName = Objects.toString(body.get("aiopsItemName"), "");
        String aiopsInstanceName = Objects.toString(body.get("aiopsInstanceName"), "");

        String newTitle = "[" + aiopsItemName + "] [" + aiopsInstanceName + "] " + title;
        body.put("newTitle", newTitle);
        String warningRowKey = Objects.toString(body.get("warningRowKey"), "");
        String rowKey = Objects.toString(body.get("rowKey"), "");
        String warningCode = Objects.toString(body.get("warningCode"), "");
        String warningName = Objects.toString(body.get("warningName"), warningCode);

        Map<String, Object> params = new HashMap<>();
        params.put("eid", eidString);
        params.put("warningName", warningName);
        params.put("warningTitle", title);
        params.put("content", newTitle);
        params.put("language", tplData.getI18n());
        params.put("warningRowKey", warningRowKey);
        params.put("rowKey", rowKey);
        warningDigiwinApp.setParams(params);
    }

    private void buildWarningSmsParams(WarningNotice.WarningSms warningSms, String tenantName, String deviceName, String title, String code) {
        Map<String, Object> params = new HashMap<>();
        if (tenantName.length() > 25) {
            tenantName = tenantName.substring(0, 25);
        }
        params.put("customerName", tenantName);
        if (deviceName.length() > 25) {
            deviceName = deviceName.substring(0, 25);
        }
        params.put("deviceName", deviceName);
        if (title.length() > 25) {
            title = title.substring(0, 25);
        }
        params.put("warningTitle", title);
        params.put("code", code);
        warningSms.setParams(params);
    }

    private void buildWarningWechatParams(WarningNotice.WarningWechat warningWechat, String title, String warningTime, String warningCount,
                                          String suggest, String url) {
        Map<String, Object> map = new HashMap<>();
        map.put("template_id", aioMailProperties.getWechat().getTemplateId());
        map.put("url", url);
        Map<String, Object> dm1 = new HashMap<>();
        dm1.put("value", "鼎捷智管家预警中心");
        Map<String, Object> dm2 = new HashMap<>();
        dm2.put("value", warningTime);
        Map<String, Object> dm3 = new HashMap<>();
        if (!StringUtils.isEmpty(title) && title.length() > 20) {
            title = title.substring(0, 20);
        }
        dm3.put("value", title);
        Map<String, Object> dm4 = new HashMap<>();
        dm4.put("value", warningCount);
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("const23", dm1);
        dataMap.put("time5", dm2);
        dataMap.put("thing8", dm3);
        dataMap.put("character_string24", dm4);
        map.put("data", dataMap);
        warningWechat.setParams(map);
    }

    private void saveNoticeMailLog(MailLog mailLog) {
        try {
            if (mailLog != null) {
                Map<String, Object> map = new LinkedHashMap<>();
                map.put("_id", mailLog.get_id());
                map.put("sourceType", mailLog.getSourceType());
                map.put("sourceId", mailLog.getSourceId());
                map.put("receivers", mailLog.getReceivers());
                map.put("ccs", mailLog.getCcs());
                map.put("subject", mailLog.getSubject());
                map.put("message", mailLog.getMessage());
                map.put("url", mailLog.getUrl());
                map.put("priority", mailLog.getPriority());
                map.put("mailStatus", mailLog.getMailStatus());
                map.put("sendTime", mailLog.getSendTime());
                map.put("processer", mailLog.getProcesser());
                map.put("tryNums", mailLog.getTryNums());
                map.put("wechatStatus", mailLog.getWehcatStatus());
                map.put("wechatMsg", mailLog.getWehcatMsg());
                map.put("smsStatus", mailLog.getSmsStatus());
                map.put("smsMsg", mailLog.getSmsMsg());
                mailService.mailSave(map);
//                mongoTemplate.insert(mailLog);
            }
        } catch (Exception ex) {
            log.error("SaveNoticeMailSendLog", ex);
        }
    }

    /**
     * 邮件通知
     *
     * @param mailLog
     * @param warningEmail
     * @throws Exception
     */
    private void noticeMail(MailLog mailLog, WarningNotice.WarningMail warningEmail) throws Exception {
        if (warningEmail == null) {
            return;
        }
        int sendCount = 1;
        boolean sendRes = false;
        while (sendCount <= maxTryNum && !sendRes) {
            try {
                MimeMessage message = getMimeMessageOfHtml(warningEmail);
                mailSender.send(message);
                sendRes = true;
                log.info("邮件发送成功~");
                log.debug("邮件发送成功：" + warningEmail);
            } catch (Exception e) {
                sendCount++;
                sendRes = false;
                log.error("邮件发送失败~");
                e.printStackTrace();
            }
        }
        buildMailLog(mailLog, warningEmail, sendRes, sendCount);
    }

    private void buildMailLog(MailLog mailLog, WarningNotice.WarningMail warningMail, boolean isSuccess, int tryNum) {
        String receivers = Arrays.stream(warningMail.getTo()).collect(Collectors.joining(";"));
        if (!ArrayUtils.isEmpty(warningMail.getCc())) {
            String ccs = Arrays.stream(warningMail.getCc()).collect(Collectors.joining(";"));
            mailLog.setCcs(ccs);
        }
        mailLog.setSourceType(MailSourceType.NoticeWarning.toString());
        mailLog.setReceivers(receivers);
        mailLog.setSubject(warningMail.getSubject());
        mailLog.setMessage(warningMail.getText());
        mailLog.setMailStatus(isSuccess ? "S" : "E");
        mailLog.setProcesser("");
        mailLog.setSendTime(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        mailLog.setTryNums(tryNum);
    }

    /**
     * 微信通知
     *
     * @param mailLog
     * @param warningWechat
     * @throws Exception
     */
    private void noticeWechat(MailLog mailLog, WarningNotice.WarningWechat warningWechat) throws Exception {
        if (warningWechat == null) {
            return;
        }
        List<String> tousers = warningWechat.getTousers();
        Map<String, Object> params = warningWechat.getParams();
        Map<String, String> userUrls = warningWechat.getUserUrls();
        if (!CollectionUtils.isEmpty(tousers)) {
            String token = wechatService.getToken();
            for (String touser : tousers) {
                params.put("touser", touser);
                String userUrl = userUrls.get(touser);
                if (!StringUtils.isEmpty(userUrl)) {
                    params.put("url", userUrl);
                }
                HashMap resultMap = wechatService.sendTemplateNotice(token, params);
                buildWechatLog(mailLog, resultMap);
            }
        }
    }

    private void buildWechatLog(MailLog mailLog, HashMap resultMap) {
        String errcode = Objects.toString(resultMap.get("errcode"), "");
        String errmsg = Objects.toString(resultMap.get("errmsg"), "");
        mailLog.setWehcatStatus(errcode);
        mailLog.setWehcatMsg(errmsg);
        log.info("微信发送~");
    }

    private void notice(Map<String, Object> body, WarningNotice warningNotice) throws Exception {
        MailLog mailLog = new MailLog();
        boolean saveLog = false;
        String noticeTypeList = Objects.toString(body.get("noticeTypeList"), "");
        if (noticeTypeList.contains("MAIL")) {
            WarningNotice.WarningMail warningMail = warningNotice.getWarningMail();
            if (warningMail != null) {
                saveLog = true;
                noticeMail(mailLog, warningMail);
            }
        }
        if (noticeTypeList.contains("WECHAT")) {
            WarningNotice.WarningWechat warningWechat = warningNotice.getWarningWechat();
            if (warningWechat != null) {
                saveLog = true;
                noticeWechat(mailLog, warningWechat);
            }
        }
        if (noticeTypeList.contains("SMS")) {
            WarningNotice.WarningSms warningSms = warningNotice.getWarningSms();
            if (warningSms != null) {
                saveLog = true;
                noticeSms(mailLog, warningSms);
            }
        }
        if (noticeTypeList.contains("DIGIWIN_APP")) {
            WarningNotice.WarningDigiwinApp warningDigiwinApp = warningNotice.getWarningDigiwinApp();
            if (warningDigiwinApp != null) {
                saveLog = true;
                noticeDigiwinApp(mailLog, warningDigiwinApp);
            }
        }
        if (saveLog) {
            saveNoticeMailLog(mailLog);
        }
    }

    /**
     * 短信通知
     *
     * @param mailLog
     * @param warningSms
     */
    private void noticeSms(MailLog mailLog, WarningNotice.WarningSms warningSms) {
        if (warningSms == null) {
            return;
        }
        List<String> phoneNumbers = warningSms.getPhoneNumbers();
        Map<String, Object> params = warningSms.getParams();
        if (!CollectionUtils.isEmpty(phoneNumbers) && !CollectionUtils.isEmpty(params)) {
            String telephones = phoneNumbers.stream().collect(Collectors.joining(";"));
            String eventId = warningSms.getEventId();
            HashMap<String, Object> map = new HashMap<>();
            map.put("data", params);
            EmcEventMsg emcEventMsg = new EmcEventMsg(eventId, telephones, map);
            IamBaseResponse responseBody = smsService.sendWarningNotice(emcEventMsg);
            buildSmsLog(mailLog, responseBody);
            //huly: 修复漏洞/bug if增加responseBody != null
            if (responseBody != null && responseBody.getCode() == 200) {
                log.info("短信发送成功~");
            } else {
                log.error("短信发送失败~");
            }
        }
    }

    private void buildSmsLog(MailLog mailLog, SendSmsResponseBody responseBody) {
        if (responseBody != null) {
            String code = responseBody.getCode();
            String message = responseBody.getMessage();
            mailLog.setSmsStatus(code);
            mailLog.setSmsMsg(message);
        }
    }

    private void buildSmsLog(MailLog mailLog, IamBaseResponse responseBody) {
        if (responseBody != null) {
            int code = responseBody.getCode();
            String message = responseBody.getMessage();
            mailLog.setSmsStatus(IntegerUtil.safeToString(code));
            mailLog.setSmsMsg(message);
        }
    }

    private FreemarkerTplData buildFreemarkerTplData(Map<String, Object> map) {
        map.put("time", map.get("warningTime"));
        // contentList 处理
        Object contentListObj = map.getOrDefault("contentList", "");
        if (contentListObj != null && contentListObj instanceof List) {
            List contentList = (List) contentListObj;
            for (Object contentOneObj : contentList) {
                if (contentOneObj != null && contentOneObj instanceof Collection) {
                    List contentOneList = (List) contentOneObj;
                    for (int i = 0; i < contentOneList.size(); i++) {
                        Object contentObj = contentOneList.get(i);
                        if (contentObj != null) {
                            contentObj = contentObj.toString();
                            contentOneList.set(i, contentObj);
                        }
                    }
                }
            }
        }
        map.put("contentList", contentListObj);
        map.put("serviceArea", serviceArea);
        map.put("misLoginUrl", misLoginUrl);
        FreemarkerTplData tplData = new FreemarkerTplData(getLanguage(map), map);
        return tplData;
    }

    private MimeMessage getMimeMessageOfHtml(WarningNotice.WarningMail warningMail) throws Exception {
        return getMimeMessage(warningMail, true);
    }

    String check = "^([a-z0-9A-Z]+[-|_|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";
    Pattern regex = Pattern.compile(check);

    public boolean checkMail(String... mails) {
        for (String mail : mails) {
            if (!regex.matcher(mail).matches()) {
                log.error("mail:{} is invalid!", mail);
                throw new RuntimeException("mail:" + mail + " is invalid!");
            }
        }
        return true;
    }

    private MimeMessage getMimeMessage(WarningNotice.WarningMail warningMail, boolean isHtml) throws Exception {
        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true);
        helper.setFrom(warningMail.getFrom());
        helper.setTo(warningMail.getTo());
        checkMail(warningMail.getTo());
        String[] cc = warningMail.getCc();
        if (ArrayUtils.isNotEmpty(cc)) {
            checkMail(cc);
            helper.setCc(cc);
        }
        helper.setSubject(warningMail.getSubject());
        helper.setText(warningMail.getText(), isHtml);
        //加载图片资源
        Resource mailHeader = new PathMatchingResourcePatternResolver()
                .getResource(ResourceUtils.CLASSPATH_URL_PREFIX + "temp/mail/" + warningMail.getI18n() + "/mailHeader.jpg");
        Resource mailFooter = new PathMatchingResourcePatternResolver()
                .getResource(ResourceUtils.CLASSPATH_URL_PREFIX + "temp/mail/" + warningMail.getI18n() + "/mailFooter.jpg");
        helper.addInline("imageMailHeader", mailHeader);
        helper.addInline("imageMailFooter", mailFooter);
        return message;
    }

    private void noticeDigiwinApp(MailLog mailLog, WarningNotice.WarningDigiwinApp warningDigiwinApp) {
        if (warningDigiwinApp == null) {
            return;
        }
        List<String> userIdList = warningDigiwinApp.getUserIdList();
        if (CollectionUtils.isEmpty(userIdList)) {
            return;
        }
        //因为无法得知那些帐号是存在的，因此改成单笔帐号发送
        userIdList.stream().forEach(x -> noticeDigiwinAppCore(warningDigiwinApp, x));
    }

    private void noticeDigiwinAppCore(WarningNotice.WarningDigiwinApp warningDigiwinApp,
                                      String userId) {
        Map<String, Object> warningParamMap = warningDigiwinApp.getParams();
        String eidString = ObjectUtils.toString(warningParamMap.get("eid"));
        Supplier<StringBuilder> sbLogSupplier = () -> {
            StringBuilder sbLog = new StringBuilder("noticeDigiwinApp eid:");
            sbLog.append(eidString);
            sbLog.append(" userId:");
            sbLog.append(userId);
            return sbLog;
        };
        if (checkIsTemporaryNoNotify(eidString)) {
            log.info(sbLogSupplier.get().append(" temporary no notify eid.").toString());
            return;
        }
        if (checkIsTemporaryNoNotify(eidString, userId)) {
            log.info(sbLogSupplier.get().append(" temporary no notify user.").toString());
            return;
        }
        AioMailProperties.DigiwinAppProperties digiwinAppProperties = aioMailProperties.getDigiwinApp();
        try {
            StringBuilder sbUrl = new StringBuilder(digiwinAppProperties.getNoticeBasicUrl());
            int length = sbUrl.length();
            //1.取得token
            sbUrl.append("/oauth/oauth/token?grant_type=");
            sbUrl.append(digiwinAppProperties.getGrantType());
            sbUrl.append("&client_id=");
            sbUrl.append(digiwinAppProperties.getClientId());
            sbUrl.append("&client_secret=");
            sbUrl.append(digiwinAppProperties.getClientSecret());
            String url = sbUrl.toString();
            Map<String, Object> bodyMap = new HashMap<>(1);
            RequestEntity<Map<String, Object>> request = RequestEntity
                    .post(URI.create(url))
                    .body(bodyMap);
            ResponseEntity<Map<String, Object>> responseEntity = restTemplate.exchange(request,
                    new ParameterizedTypeReference<Map<String, Object>>() {
                    });
            Map<String, Object> map = responseEntity.getBody();
            String token = getValStr(map, "access_token");
            //2.发送消息
            sbUrl.setLength(length);
            sbUrl.append("/openapi/invoke/dinghui/notice/send?access_token=");
            sbUrl.append(token);
            url = sbUrl.toString();
            Map<String, Object> stdDataMap = new HashMap<>(1);
            Map<String, Object> parameterMap = new HashMap<>();
            String language = ObjectUtils.toString(warningParamMap.get("language"));
            StringBuilder sbDetailUrl = new StringBuilder(digiwinAppProperties.getBasicUrl());
            sbDetailUrl.append("/#/pages/inform-group/pages/warning-message/warning-message?token=${mobile_token}&locale=${locale}&key=");
            sbDetailUrl.append(URLEncoder.encode(ObjectUtils.toString(warningParamMap.get("warningRowKey")),
                    "UTF-8").replace("+", "%20"));
            parameterMap.put("detail_url", sbDetailUrl.toString());
            parameterMap.put("is_system", false);
            parameterMap.put("company_id", "");
            parameterMap.put("service_name", "dinghui.notice.send");
            parameterMap.put("locale", language);
            parameterMap.put("title", warningParamMap.get("warningName"));
            parameterMap.put("content", warningParamMap.get("content"));
            parameterMap.put("to_person_ids", "");
            parameterMap.put("src_system_access_token", "");
            parameterMap.put("access_token", "");
            parameterMap.put("category_key", "AIEOM");
            parameterMap.put("sub_category_key", "WARNING");
            parameterMap.put("outer_tenant_sid", eidString);
            //因为无法得知那些帐号是存在的，因此改成单笔帐号发送
//            parameterMap.put("outer_user_id", userIdList.get(0));
            parameterMap.put("outer_user_id", userId);
//            int size = userIdList.size();
//            if (size > 1) {
//                parameterMap.put("outer_user_ids", userIdList.stream().skip(1).collect(Collectors.toList()));
//            } else {
//                parameterMap.put("outer_user_ids", new String[]{});
//            }
            parameterMap.put("outer_user_ids", new String[]{});
            parameterMap.put("group_id", "");
            /* 填此值是为了让消息最左边的圆圈变成蓝色圆圈
             * 具体规则
             * 将creator_name的内容值转为Unicode
             * (取最后两字)然后再转为16进位后再除于8
             * 依照结果分配色块
             * #17c295(绿)、#4da9eb(浅蓝)、#f7b55e(黄)、#f2725e(红)、#5299cc(深蓝)、#e66b9e(桃红)、#808080(灰)
             * 范例：
             * 空字串:红
             * 空白:绿
             */
            parameterMap.put("creator_name", "AIEOM Warning Center");
            parameterMap.put("id", "");
            parameterMap.put("forward_application_id", "");
            stdDataMap.put("parameter", parameterMap);
            bodyMap.put("std_data", stdDataMap);
            request = RequestEntity
                    .post(URI.create(url))
                    .body(bodyMap);
            responseEntity = restTemplate.exchange(request,
                    new ParameterizedTypeReference<Map<String, Object>>() {
                    });
            map = responseEntity.getBody();
            Optional<Map<String, Object>> optDataMap = MapUtil.getMap(map, "std_data");
            if (!optDataMap.isPresent()) {
                return;
            }
            map = optDataMap.get();
            optDataMap = MapUtil.getMap(map, "execution");
            if (!optDataMap.isPresent()) {
                return;
            }
            map = optDataMap.get();
            String code = ObjectUtils.toString(map.get("code"));
            if (!"0".equals(code)) {
                String description = ObjectUtils.toString(map.get("description"));
                log.error(sbLogSupplier.get().append(" url:").append(url).append(" \n body:")
                        .append(JSONObject.toJSONString(bodyMap))
                        .append(" \n return code:")
                        .append(code).append(" not zero, description:")
                        .append(description).toString());
                if (!StringUtils.isEmpty(description)) {
                    if (description.indexOf("SID(") >= 0) {
                        //如果发送失败，且描述中包含「SID(」，就暂时停止发送给该租户Sid
                        //范例:指定正确且存在的租户SID(或租户SID)或确保对应社区已经创建！
                        saveTemporaryNoNotify(eidString);
                        return;
                    }
                    if (description.indexOf(userId) >= 0 || description.indexOf("outer_user_id") >= 0) {
                        //如果发送失败，且描述中包含userId或者outer_user_id，就暂时停止发送给该租户Sid下的用户
                        saveTemporaryNoNotify(eidString, userId);
                    }
                }
                return;
            }
        } catch (Exception ex) {
            log.error(sbLogSupplier.get().append(" exception:").toString(), ex);
            return;
        }
    }

    private boolean checkIsTemporaryNoNotify(String eidString, String userId) {
        String key = getDigiwinAppNoNotifyKey(eidString, userId, false);
        return !StringUtils.isEmpty(stringRedisTemplate.opsForValue().get(key));
    }

    private void saveTemporaryNoNotify(String eidString, String userId) {
        saveTemporaryNoNotifyCore(getDigiwinAppNoNotifyKey(eidString, userId, false));
    }

    private void saveTemporaryNoNotifyCore(String key) {
        //写入短暂不发送纪录，避免不停尝试失败
        String temporaryNoNotifyHoursString = env.getProperty("temporary.no.notify.hours");
        int temporaryNoNotifyHours;
        if ("0".equals(temporaryNoNotifyHoursString)) {
            //如果是0，那就不存入redis，直接返回
            return;
        } else {
            temporaryNoNotifyHours = IntegerUtil.objectToInteger(temporaryNoNotifyHoursString);
            if (IntegerUtil.isEmpty(temporaryNoNotifyHours)) {
                temporaryNoNotifyHours = TEMPORARY_NO_NOTIFY_HOURS;
            }
        }
        stringRedisTemplate.opsForValue().set(key, "1", temporaryNoNotifyHours, TimeUnit.HOURS);
    }

    private boolean checkIsTemporaryNoNotify(String eidString) {
        String key = getDigiwinAppNoNotifyKey(eidString, false);
        return !StringUtils.isEmpty(stringRedisTemplate.opsForValue().get(key));
    }

    private void saveTemporaryNoNotify(String eidString) {
        saveTemporaryNoNotifyCore(getDigiwinAppNoNotifyKey(eidString, false));
    }

    private String buildWeChatUrl(String baseUrl, String warningRowKey, String userId) throws Exception {
        HashMap<String, Object> map = new HashMap<>();
        map.put("key", warningRowKey);
//        map.put("token", commonUtils.getAioToken());
        map.put("showDetail", "false");
        if (!StringUtils.isEmpty(userId)) {
            map.put("userId", userId);
        }
        return baseUrl + "?enParam=" + URLEncoder.encode(Base64.getEncoder().encodeToString(EncryptionUtil.encrypt(JSON.toJSONString(map).getBytes(), EncryptionUtil.key.getBytes())), "UTF-8");
    }

    public String getLanguage(Map<String, Object> map) {
        Object language = map.get("language");
        if (language != null) {
            return messageUtils.getLanguage(language.toString());
        }
        String property = env.getProperty("language");
        if (!StringUtils.isEmpty(property)) {
            return MessageUtils.getLanguage(property);
        }
        return MessageUtils.ZH_CN_STANDARD;
    }

    public String getValStr(Map<String, Object> body, String key) {
        return body.get(key) != null ? body.get(key).toString() : "";
    }

    private Environment env;

    @Override
    public void setEnvironment(Environment environment) {
        this.env = environment;
    }
}
