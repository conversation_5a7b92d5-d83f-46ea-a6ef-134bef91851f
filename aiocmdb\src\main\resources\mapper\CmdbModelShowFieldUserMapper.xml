<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aiocmdb.asset.dao.CmdbModelShowFieldUserMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.digiwin.escloud.aiocmdb.asset.model.CmdbModelShowFieldUser">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="sid" property="sid" jdbcType="BIGINT"/>
        <result column="modelCode" property="modelCode" jdbcType="VARCHAR"/>
        <result column="userId" property="userId" jdbcType="VARCHAR"/>
        <result column="fieldCode" property="fieldCode" jdbcType="VARCHAR"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
        <result column="modelFieldGroupCode" property="modelFieldGroupCode" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入用户字段配置 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO cmdb_model_show_field_user 
        (id, sid, modelCode, userId, fieldCode, sort, modelFieldGroupCode)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.sid}, #{item.modelCode}, #{item.userId}, 
             #{item.fieldCode}, #{item.sort}, #{item.modelFieldGroupCode})
        </foreach>
    </insert>

    <!-- 删除用户字段配置 -->
    <delete id="deleteByModelCodeAndUserId">
        DELETE FROM cmdb_model_show_field_user 
        WHERE model_code = #{modelCode} 
        AND user_id = #{userId} 
        AND sid = #{sid}
    </delete>

    <!-- 查询用户字段配置 -->
    <select id="selectByModelCodeAndUserId" resultMap="BaseResultMap">
        SELECT id, sid, modelCode, userId, fieldCode, sort, modelFieldGroupCode
        FROM cmdb_model_show_field_user 
        WHERE model_code = #{modelCode} 
        AND user_id = #{userId} 
        AND sid = #{sid}
        ORDER BY sort ASC
    </select>

</mapper>
